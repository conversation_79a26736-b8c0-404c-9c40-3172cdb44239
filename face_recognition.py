from tkinter import ttk
from tkinter import *
from tkinter import messagebox as msgbox
from PIL import Image, ImageTk
import cv2
import os
import mysql.connector
class Face_Recognition:
    def __init__(self, root):
        self.root = root
        self.root.geometry("1530x790+0+0")
        self.root.title("Face Recognition")

        # Title label
        title_lbl = Label(self.root, text="FACE RECOGNITION", font=("arial", 25, "bold"), bg="white", fg="red")
        title_lbl.place(x=0, y=0, width=1530, height=47)
#  1st image
        img_left = Image.open(r"Project_images\face_detector1.jpg")
        img_left = img_left.resize((650, 700), Image.LANCZOS)
        self.photo_left = ImageTk.PhotoImage(img_left)
        
        f_lbl = Label(self.root, image=self.photo_left)
        f_lbl.place(x=0, y=55, width=650, height=700)
        
#  2nd image        
        img_right = Image.open(r"Project_images\facial_recognition_system_identification_digital_id_security_scanning_thinkstock_858236252_3x3-100740902-large.jpg")
        img_right = img_right.resize((950, 700), Image.LANCZOS)
        self.photo_right = ImageTk.PhotoImage(img_right)
        
        f_lbl = Label(self.root, image=self.photo_right)
        f_lbl.place(x=650, y=55, width=950, height=700)
# button
        b1=Button(self.root,text="FACE RECOGNITION", command=self.face_recog, cursor="hand2",font=("times new roman",15,"bold"),bg="darkgreen",fg="white")
        b1.place(x=1012,y=680,width=220,height=41)

#==========Face Recognition Function==========
    def face_recog(self):
        def draw_boundary(img, classifier, scaleFactor, minNeighbors, color, text, clf):
            gray_image = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            features = classifier.detectMultiScale(gray_image, scaleFactor, minNeighbors)

            coord = []

            for (x, y, w, h) in features:
                cv2.rectangle(img, (x, y), (x+w, y+h), (0, 255, 0), 3)
                id, predict = clf.predict(gray_image[y:y+h, x:x+w])
                confidence = int(100 * (1 - predict / 300))

                # Debug: Show prediction values
                print(f"Detected ID: {id}, Raw confidence: {predict}, Calculated confidence: {confidence}%")

                # Initialize default values
                n = "Unknown"
                r = "Unknown"
                d = "Unknown"

                # Try to get student info from database
                try:
                    conn = mysql.connector.connect(host="localhost", username="root", password="Admin@1402", database="face_recognizer")
                    my_cursor = conn.cursor()

                    # Check if student exists
                    my_cursor.execute("SELECT Name, Roll, Dep FROM student WHERE Student_id = %s", (id,))
                    result = my_cursor.fetchone()

                    if result:
                        n, r, d = result
                        print(f"Found student: {n}, Roll: {r}, Dep: {d}")
                    else:
                        print(f"No student found with ID: {id}")

                    conn.close()

                except Exception as e:
                    print(f"Database error: {e}")

                # Lower confidence threshold for better recognition
                if confidence > 50:  # Changed from 77 to 50
                    cv2.putText(img, f"Name: {n}", (x, y-190), cv2.FONT_HERSHEY_COMPLEX, 0.8, (255, 255, 255), 3)
                    cv2.putText(img, f"Roll: {r}", (x, y-165), cv2.FONT_HERSHEY_COMPLEX, 0.8, (255, 255, 255), 3)
                    cv2.putText(img, f"Department: {d}", (x, y-135), cv2.FONT_HERSHEY_COMPLEX, 0.8, (255, 255, 255), 3)
                    cv2.putText(img, f"Student ID: {id}", (x, y-105), cv2.FONT_HERSHEY_COMPLEX, 0.8, (255, 255, 255), 3)
                    cv2.putText(img, f"Conf: {confidence}%", (x, y+h+20), cv2.FONT_HERSHEY_COMPLEX, 0.6, (0, 255, 0), 2)
                else:
                    cv2.rectangle(img, (x, y), (x+w, y+h), (0, 0, 255), 3)
                    cv2.putText(img, "Unknown Face", (x, y-5), cv2.FONT_HERSHEY_COMPLEX, 0.8, (0, 0, 255), 3)
                    cv2.putText(img, f"Conf: {confidence}%", (x, y+h+20), cv2.FONT_HERSHEY_COMPLEX, 0.6, (0, 0, 255), 2)

                coord.append([x, y, w, h])

            return coord
        def recognize(img, clf, faceCascade):
            # Use optimal face detection parameters based on training image analysis
            draw_boundary(img, faceCascade, 1.1, 5, (255, 255, 255), "Face", clf)
            return img

        try:
            faceCascade = cv2.CascadeClassifier("haarcascade_frontalface_default.xml")
            if faceCascade.empty():
                msgbox.showerror("Error", "Face cascade file not found!", parent=self.root)
                return

            clf = cv2.face.LBPHFaceRecognizer_create()
            clf.read("classifier.xml")

            # Use camera 1 (known working) with fallback to camera 0
            video_capture = cv2.VideoCapture(1)
            if not video_capture.isOpened():
                video_capture = cv2.VideoCapture(0)

            if not video_capture.isOpened():
                msgbox.showerror("Error", "Cannot open camera!", parent=self.root)
                return

            msgbox.showinfo("Info", "Face recognition started. Press ESC to stop.", parent=self.root)

            while True:
                ret, img = video_capture.read()
                if not ret:
                    print("Failed to read from camera")
                    break

                img = recognize(img, clf, faceCascade)
                cv2.imshow("Welcome To Face Recognition", img)

                # Press ESC to exit (key code 27)
                if cv2.waitKey(1) & 0xFF == 27:
                    break

            # Clean up (moved outside the while loop)
            video_capture.release()
            cv2.destroyAllWindows()

        except Exception as e:
            msgbox.showerror("Error", f"Face recognition failed: {str(e)}", parent=self.root)
       
if __name__ == "__main__":
    root=Tk()
    obj=Face_Recognition(root)
    root.mainloop()            