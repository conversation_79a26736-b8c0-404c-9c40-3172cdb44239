from tkinter import ttk
from tkinter import *
from tkinter import messagebox as msgbox
from PIL import Image, ImageTk
import cv2
import os
import mysql.connector
class Face_Recognition:
    def __init__(self, root):
        self.root = root
        self.root.geometry("1530x790+0+0")
        self.root.title("Face Recognition")

        # Title label
        title_lbl = Label(self.root, text="FACE RECOGNITION", font=("arial", 25, "bold"), bg="white", fg="red")
        title_lbl.place(x=0, y=0, width=1530, height=47)
#  1st image
        img_left = Image.open(r"Project_images\face_detector1.jpg")
        img_left = img_left.resize((650, 700), Image.LANCZOS)
        self.photo_left = ImageTk.PhotoImage(img_left)
        
        f_lbl = Label(self.root, image=self.photo_left)
        f_lbl.place(x=0, y=55, width=650, height=700)
        
#  2nd image        
        img_right = Image.open(r"Project_images\facial_recognition_system_identification_digital_id_security_scanning_thinkstock_858236252_3x3-100740902-large.jpg")
        img_right = img_right.resize((950, 700), Image.LANCZOS)
        self.photo_right = ImageTk.PhotoImage(img_right)
        
        f_lbl = Label(self.root, image=self.photo_right)
        f_lbl.place(x=650, y=55, width=950, height=700)
# button
        b1=Button(self.root,text="FACE RECOGNITION", command=self.face_recog, cursor="hand2",font=("times new roman",15,"bold"),bg="darkgreen",fg="white")
        b1.place(x=1012,y=680,width=220,height=41)

#==========Face Recognition Function==========
    def face_recog(self):
        def draw_boundary(img, classifier, scaleFactor, minNeighbors, color, text, clf):
            # Check if image is valid
            if img is None or img.size == 0:
                print("Invalid image passed to draw_boundary")
                return []

            try:
                gray_image = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
                features = classifier.detectMultiScale(gray_image, scaleFactor, minNeighbors)
            except Exception as e:
                print(f"Error in image processing: {str(e)}")
                return []

            coord = []

            for (x, y, w, h) in features:
                cv2.rectangle(img, (x, y), (x+w, y+h), (0, 255, 0), 3)

                try:
                    id, predict = clf.predict(gray_image[y:y+h, x:x+w])
                    confidence = int(100 * (1 - predict / 300))

                    # Initialize default values
                    n = "Unknown"
                    r = "Unknown"
                    d = "Unknown"
                    i = "Unknown"

                    try:
                        conn = mysql.connector.connect(host="localhost", username="root", password="Admin@1402", database="face_recognizer")
                        my_cursor = conn.cursor()

                        my_cursor.execute("select Name from student where Student_id=" + str(id))
                        name_result = my_cursor.fetchone()
                        if name_result:
                            n = "+".join(name_result)

                        my_cursor.execute("select Roll from student where Student_id=" + str(id))
                        roll_result = my_cursor.fetchone()
                        if roll_result:
                            r = "+".join(roll_result)

                        my_cursor.execute("select Dep from student where Student_id=" + str(id))
                        dep_result = my_cursor.fetchone()
                        if dep_result:
                            d = "+".join(dep_result)

                        my_cursor.execute("select Student_id from student where Student_id=" + str(id))
                        id_result = my_cursor.fetchone()
                        if id_result:
                            i = "+".join(id_result)

                        conn.close()

                    except mysql.connector.Error as db_error:
                        print(f"Database error: {db_error}")
                        # Continue with default values
                    except Exception as db_error:
                        print(f"Database connection error: {db_error}")
                        # Continue with default values

                    if confidence > 77:
                        cv2.putText(img, f"Name: {n}", (x, y - 55), cv2.FONT_HERSHEY_COMPLEX, 0.8, (255, 255, 255), 3)
                        cv2.putText(img, f"Roll: {r}", (x, y - 30), cv2.FONT_HERSHEY_COMPLEX, 0.8, (255, 255, 255), 3)
                        cv2.putText(img, f"Department: {d}", (x, y - 10), cv2.FONT_HERSHEY_COMPLEX, 0.8, (255, 255, 255), 3)
                        cv2.putText(img, f"Student ID: {i}", (x, y - 75), cv2.FONT_HERSHEY_COMPLEX, 0.8, (255, 255, 255), 3)
                    else:
                        cv2.rectangle(img, (x, y), (x+w, y+h), (0, 0, 255), 3)
                        cv2.putText(img, "Unknown Face", (x, y - 5), cv2.FONT_HERSHEY_COMPLEX, 0.8, (255, 255, 255), 3)

                except Exception as pred_error:
                    print(f"Prediction error: {pred_error}")
                    # Draw a simple rectangle for detected face
                    cv2.rectangle(img, (x, y), (x+w, y+h), (0, 0, 255), 3)
                    cv2.putText(img, "Detection Error", (x, y - 5), cv2.FONT_HERSHEY_COMPLEX, 0.8, (255, 255, 255), 3)

                coord = [x, y, w, h]

            return coord    
        def recognize(img, clf, faceCascade):
            draw_boundary(img, faceCascade, 1.1, 10, (255, 255, 255), "Face", clf)
            return img
        try:
            faceCascade = cv2.CascadeClassifier("haarcascade_frontalface_default.xml")
            if faceCascade.empty():
                msgbox.showerror("Error", "Could not load haarcascade_frontalface_default.xml")
                return

            clf = cv2.face.LBPHFaceRecognizer_create()
            clf.read("classifier.xml")
        except Exception as e:
            msgbox.showerror("Error", f"Failed to load classifier files: {str(e)}")
            return

        video_cap = cv2.VideoCapture(0)

        # Check if camera opened successfully
        if not video_cap.isOpened():
            msgbox.showerror("Error", "Could not open camera")
            return

        # Set camera properties for better performance
        video_cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        video_cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        video_cap.set(cv2.CAP_PROP_FPS, 30)

        print("Camera initialized successfully")
        print("Press Enter or Esc to exit the face recognition")

        try:
            frame_count = 0
            while True:
                ret, img = video_cap.read()
                frame_count += 1

                # Check if frame was successfully captured
                if not ret:
                    print("Failed to capture frame from camera")
                    break

                # Check if image is not empty
                if img is None or img.size == 0:
                    print("Empty frame received")
                    continue

                # Debug: Print frame info every 30 frames
                if frame_count % 30 == 0:
                    print(f"Processing frame {frame_count}, shape: {img.shape}")

                try:
                    img = recognize(img, clf, faceCascade)

                    # Add frame counter to the display
                    cv2.putText(img, f"Frame: {frame_count}", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
                    cv2.putText(img, "Press Enter or Esc to exit", (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

                    # Create named window with specific properties
                    window_name = "Face Recognition - Camera Feed"
                    cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
                    cv2.resizeWindow(window_name, 800, 600)
                    cv2.moveWindow(window_name, 100, 100)
                    cv2.imshow(window_name, img)
                except Exception as e:
                    print(f"Error in recognition: {str(e)}")
                    # Show the original frame if recognition fails
                    window_name = "Face Recognition - Camera Feed"
                    cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
                    cv2.resizeWindow(window_name, 800, 600)
                    cv2.moveWindow(window_name, 100, 100)
                    cv2.imshow(window_name, img)
                    continue

                # Use cv2.waitKey with error handling
                try:
                    key = cv2.waitKey(1) & 0xFF
                    if key == 13 or key == 27:  # Enter or Esc key to exit
                        break
                except Exception as e:
                    print(f"Error in waitKey: {str(e)}")
                    break

        except Exception as e:
            print(f"Error in main loop: {str(e)}")
            msgbox.showerror("Error", f"An error occurred: {str(e)}")
        finally:
            video_cap.release()
            cv2.destroyAllWindows()

                

               
if __name__ == "__main__":
    root=Tk()
    obj=Face_Recognition(root)
    root.mainloop()            