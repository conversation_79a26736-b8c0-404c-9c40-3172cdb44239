import cv2
import numpy as np

def test_camera():
    print("Testing camera access...")
    
    # Try different camera indices
    for camera_index in range(3):
        print(f"Trying camera index {camera_index}...")
        cap = cv2.VideoCapture(camera_index)
        
        if cap.isOpened():
            print(f"Camera {camera_index} opened successfully!")
            
            # Test frame capture
            ret, frame = cap.read()
            if ret and frame is not None:
                print(f"Frame captured successfully from camera {camera_index}")
                print(f"Frame shape: {frame.shape}")
                print(f"Frame dtype: {frame.dtype}")
                
                # Display the frame
                cv2.imshow(f'Camera Test - Index {camera_index}', frame)
                print("Press any key to continue to next camera or ESC to exit...")
                key = cv2.waitKey(0) & 0xFF
                cv2.destroyAllWindows()
                
                if key == 27:  # ESC key
                    cap.release()
                    return camera_index
            else:
                print(f"Failed to capture frame from camera {camera_index}")
        else:
            print(f"Failed to open camera {camera_index}")
        
        cap.release()
    
    print("No working camera found!")
    return None

def test_live_feed(camera_index=0):
    print(f"Testing live feed from camera {camera_index}...")
    cap = cv2.VideoCapture(camera_index)
    
    if not cap.isOpened():
        print("Error: Could not open camera")
        return
    
    # Set camera properties
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    cap.set(cv2.CAP_PROP_FPS, 30)
    
    print("Camera opened. Press 'q' to quit, 's' to save a test image")
    
    frame_count = 0
    while True:
        ret, frame = cap.read()
        
        if not ret:
            print("Failed to grab frame")
            break
        
        frame_count += 1
        
        # Add frame info overlay
        cv2.putText(frame, f"Frame: {frame_count}", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        cv2.putText(frame, "Press 'q' to quit, 's' to save", (10, 70), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        cv2.imshow('Live Camera Feed', frame)
        
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
        elif key == ord('s'):
            cv2.imwrite('test_frame.jpg', frame)
            print("Test frame saved as 'test_frame.jpg'")
    
    cap.release()
    cv2.destroyAllWindows()
    print("Camera test completed")

if __name__ == "__main__":
    print("=== Camera Diagnostic Tool ===")
    
    # Test camera access
    working_camera = test_camera()
    
    if working_camera is not None:
        print(f"\nUsing camera index {working_camera} for live test...")
        test_live_feed(working_camera)
    else:
        print("No working camera found. Please check:")
        print("1. Camera is connected and not used by other applications")
        print("2. Camera permissions are granted")
        print("3. Camera drivers are installed")
