import mysql.connector

def test_database_connection():
    """Test database connection and check student data"""
    try:
        print("Testing database connection...")
        conn = mysql.connector.connect(
            host="localhost", 
            username="root", 
            password="Admin@1402", 
            database="face_recognizer"
        )
        print("✅ Database connected successfully!")
        
        cursor = conn.cursor()
        
        # Check if student table exists
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        print(f"Tables in database: {tables}")
        
        # Check student table structure
        cursor.execute("DESCRIBE student")
        columns = cursor.fetchall()
        print(f"Student table structure: {columns}")
        
        # Check student data
        cursor.execute("SELECT * FROM student")
        students = cursor.fetchall()
        print(f"Number of students in database: {len(students)}")
        
        if students:
            print("Student data:")
            for student in students:
                print(f"  {student}")
        else:
            print("⚠️  No student data found in database!")
        
        # Test specific queries that face recognition uses
        print("\nTesting face recognition queries...")
        test_ids = [1, 2, 3]  # Common student IDs
        
        for test_id in test_ids:
            print(f"\nTesting Student ID: {test_id}")
            
            cursor.execute("SELECT Name FROM student WHERE Student_id=" + str(test_id))
            name = cursor.fetchone()
            print(f"  Name: {name}")
            
            cursor.execute("SELECT Roll FROM student WHERE Student_id=" + str(test_id))
            roll = cursor.fetchone()
            print(f"  Roll: {roll}")
            
            cursor.execute("SELECT Dep FROM student WHERE Student_id=" + str(test_id))
            dep = cursor.fetchone()
            print(f"  Department: {dep}")
        
        conn.close()
        print("\n✅ Database test completed successfully!")
        
    except mysql.connector.Error as e:
        print(f"❌ Database error: {e}")
    except Exception as e:
        print(f"❌ Connection error: {e}")

def check_classifier_file():
    """Check if classifier file exists and is valid"""
    import cv2
    import os
    
    print("\nTesting classifier file...")
    
    if not os.path.exists("classifier.xml"):
        print("❌ classifier.xml not found!")
        return False
    
    try:
        recognizer = cv2.face.LBPHFaceRecognizer_create()
        recognizer.read("classifier.xml")
        print("✅ classifier.xml loaded successfully!")
        return True
    except Exception as e:
        print(f"❌ Error loading classifier.xml: {e}")
        return False

def check_haarcascade_file():
    """Check if haarcascade file exists and is valid"""
    import cv2
    import os
    
    print("\nTesting haarcascade file...")
    
    if not os.path.exists("haarcascade_frontalface_default.xml"):
        print("❌ haarcascade_frontalface_default.xml not found!")
        return False
    
    try:
        cascade = cv2.CascadeClassifier("haarcascade_frontalface_default.xml")
        if cascade.empty():
            print("❌ haarcascade_frontalface_default.xml is empty or invalid!")
            return False
        else:
            print("✅ haarcascade_frontalface_default.xml loaded successfully!")
            return True
    except Exception as e:
        print(f"❌ Error loading haarcascade_frontalface_default.xml: {e}")
        return False

if __name__ == "__main__":
    print("=== Face Recognition System Diagnostic ===")
    
    # Test database
    test_database_connection()
    
    # Test classifier
    classifier_ok = check_classifier_file()
    
    # Test haarcascade
    haarcascade_ok = check_haarcascade_file()
    
    print("\n=== Summary ===")
    if classifier_ok and haarcascade_ok:
        print("✅ All required files are working!")
        print("💡 If face recognition still doesn't work, the issue might be:")
        print("   1. Low confidence threshold")
        print("   2. Poor lighting conditions")
        print("   3. Face not trained in the model")
        print("   4. Camera quality/angle")
    else:
        print("❌ Some required files are missing or corrupted!")
        print("💡 Please check the missing files and retrain the model if needed.")
