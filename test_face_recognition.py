import cv2
import os

def test_face_recognition_simple():
    """Test face recognition without database operations"""
    
    # Check if required files exist
    if not os.path.exists("haarcascade_frontalface_default.xml"):
        print("Error: haarcascade_frontalface_default.xml not found")
        return
    
    if not os.path.exists("classifier.xml"):
        print("Error: classifier.xml not found")
        return
    
    # Load cascade classifier
    face_cascade = cv2.CascadeClassifier("haarcascade_frontalface_default.xml")
    if face_cascade.empty():
        print("Error: Could not load haarcascade_frontalface_default.xml")
        return
    
    # Load face recognizer
    try:
        recognizer = cv2.face.LBPHFaceRecognizer_create()
        recognizer.read("classifier.xml")
        print("Face recognizer loaded successfully")
    except Exception as e:
        print(f"Error loading face recognizer: {e}")
        return
    
    # Initialize camera
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        print("Error: Could not open camera")
        return
    
    print("Face recognition test started. Press 'q' to quit")
    
    frame_count = 0
    while True:
        ret, frame = cap.read()
        
        if not ret:
            print("Failed to capture frame")
            break
        
        frame_count += 1
        
        # Convert to grayscale
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # Detect faces
        faces = face_cascade.detectMultiScale(gray, 1.1, 4)
        
        # Process each detected face
        for (x, y, w, h) in faces:
            # Draw rectangle around face
            cv2.rectangle(frame, (x, y), (x+w, y+h), (0, 255, 0), 2)
            
            try:
                # Extract face region
                face_roi = gray[y:y+h, x:x+w]
                
                # Predict using the recognizer
                id, confidence = recognizer.predict(face_roi)
                confidence_percent = int(100 * (1 - confidence / 300))
                
                # Display results
                if confidence_percent > 77:
                    cv2.putText(frame, f"ID: {id}", (x, y-40), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
                    cv2.putText(frame, f"Confidence: {confidence_percent}%", (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                else:
                    cv2.rectangle(frame, (x, y), (x+w, y+h), (0, 0, 255), 2)
                    cv2.putText(frame, "Unknown", (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
                    
            except Exception as e:
                print(f"Recognition error: {e}")
                cv2.putText(frame, "Error", (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
        
        # Add frame info
        cv2.putText(frame, f"Frame: {frame_count}", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(frame, f"Faces: {len(faces)}", (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(frame, "Press 'q' to quit", (10, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        # Display frame
        cv2.imshow('Face Recognition Test', frame)
        
        # Check for exit
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q') or key == 27:
            break
    
    cap.release()
    cv2.destroyAllWindows()
    print("Face recognition test completed")

if __name__ == "__main__":
    test_face_recognition_simple()
